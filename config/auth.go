package config

import (
	"bufio"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"

	"github.com/dgrijalva/jwt-go"
)

type JWTAuth struct {
	PrivateKey   *rsa.PrivateKey
	PublicKey    *rsa.PublicKey
	Method       jwt.SigningMethod
	MethodString string
}

func GetAuthConfig() JWTAuth {
	privateKeyPath := os.Getenv("PRIVATE_KEY_PATH")
	publicKeyPath := os.Getenv("PUBLIC_KEY_PATH")

	if privateKeyPath == "" {
		privateKeyPath = "assets/rsa/priv.key"
	}
	if publicKeyPath == "" {
		publicKeyPath = "assets/rsa/pub.key"
	}

	checkOrCreateKeys(privateKeyPath, publicKeyPath)
	return JWTAuth{
		PrivateKey:   readPrivateKey(privateKeyPath),
		PublicKey:    readPublicKey(publicKeyPath),
		Method:       jwt.SigningMethodRS512,
		MethodString: "RS512",
	}
}

func readPrivateKey(filePath string) *rsa.PrivateKey {
	privateKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := privateKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(privateKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode(pemBytes)

	privateKeyFile.Close()

	privateKeyImported, err := x509.ParsePKCS1PrivateKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	return privateKeyImported
}

func readPublicKey(filePath string) *rsa.PublicKey {
	publicKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := publicKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(publicKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode(pemBytes)

	publicKeyFile.Close()

	publicKeyImported, err := x509.ParsePKIXPublicKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	rsaPub, ok := publicKeyImported.(*rsa.PublicKey)

	if !ok {
		panic(err)
	}

	return rsaPub
}

func checkOrCreateKeys(privateKeyPath string, publicKeyPath string) {
	if os.Getenv("ENV") != "localhost" {
		fmt.Println("Not localhost, skipping key generation.")
		fmt.Println("--------------------------------------------------------------")
		fmt.Println("## TIPS: change your environemnt to localhost to automatically generate keys.")
		fmt.Println("--------------------------------------------------------------")
		return
	}

	// Check if the file exists
	if _, err := os.Stat(privateKeyPath); os.IsNotExist(err) {
		fmt.Println("File does not exist:", privateKeyPath)
	} else {
		fmt.Println("File exists:", privateKeyPath)
		return
	}

	// Generate a new RSA key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating key:", err)
		return
	}
	fmt.Println("Generated new RSA key pair.")

	// Encode the private key in PEM format
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	// Ensure the directory exists before creating the private key file
	privateKeyDir := ""
	if idx := len(privateKeyPath) - 1; idx >= 0 {
		for i := idx; i >= 0; i-- {
			if privateKeyPath[i] == '/' {
				privateKeyDir = privateKeyPath[:i]
				break
			}
		}
	}
	if privateKeyDir != "" {
		if err := os.MkdirAll(privateKeyDir, 0755); err != nil {
			fmt.Printf("Error creating directory for private key: %v, path: %s\n", err, privateKeyDir)
			return
		}
	}
	privateKeyFile, err := os.Create(privateKeyPath)
	if err != nil {
		fmt.Printf("Error creating private key file: %v, path: %s\n", err, privateKeyPath)
		return
	}
	defer privateKeyFile.Close()
	if err := pem.Encode(privateKeyFile, privateKeyBlock); err != nil {
		fmt.Printf("Error encoding private key: %v, path: %s\n", err, privateKeyPath)
		return
	}
	fmt.Println("Created private key file:", privateKeyPath)

	// Encode the public key in PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		fmt.Println("Error marshalling public key:", err)
		return
	}
	publicKeyBlock := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	publicKeyFile, err := os.Create(publicKeyPath)
	if err != nil {
		fmt.Println("Error creating public key file:", err)
		return
	}
	defer publicKeyFile.Close()
	if err := pem.Encode(publicKeyFile, publicKeyBlock); err != nil {
		fmt.Println("Error encoding public key:", err)
		return
	}

	fmt.Println("RSA key pair generated successfully!")
}

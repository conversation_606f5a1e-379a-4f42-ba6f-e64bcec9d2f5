package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLOvertimeRepository struct {
	mysql.Repository
}

// NewMySQLOvertimeRepository creates a new MySQL overtime repository
func NewMySQLOvertimeRepository(conn *sql.DB) domain.OvertimeRepository {
	return &mySQLOvertimeRepository{mysql.Repository{Conn: conn}}
}

// AddOvertimePolicy creates a new overtime policy (simple implementation)
func (m *mySQLOvertimeRepository) AddOvertimePolicy(policy domain.OvertimePolicyRequest, adminFkid int) (int64, error) {
	return m.AddOvertimePolicyWithTiers(policy, adminFkid)
}

// AddOvertimePolicyWithTiers creates a new overtime policy with optional tiers using transaction
func (m *mySQLOvertimeRepository) AddOvertimePolicyWithTiers(policy domain.OvertimePolicyRequest, adminFkid int) (int64, error) {
	var policyID int64

	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Prepare overtime policy data
		now := time.Now().UnixMilli()
		policyData := map[string]interface{}{
			"policy_name":   policy.PolicyName,
			"description":   policy.Description,
			"rounding_type": policy.RoundingType,
			"admin_fkid":    adminFkid,
			"created_at":    now,
			"updated_at":    now,
		}

		// Handle rounding fields based on type
		switch policy.RoundingType {
		case "none":
			policyData["rounding_minutes"] = nil
			policyData["rounding_method"] = nil
		case "simple":
			if policy.RoundingMinutes == nil || policy.RoundingMethod == nil {
				return fmt.Errorf("rounding_minutes and rounding_method are required for simple rounding type")
			}
			policyData["rounding_minutes"] = *policy.RoundingMinutes
			policyData["rounding_method"] = *policy.RoundingMethod
		case "tiered":
			policyData["rounding_minutes"] = nil
			policyData["rounding_method"] = nil
			if len(policy.RoundingTiers) == 0 {
				return fmt.Errorf("rounding_tiers are required for tiered rounding type")
			}
		}

		// Insert overtime policy record
		result := tx.Insert("hrm_overtime_policies", policyData)
		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("failed to get last insert ID: %v", err)
		}

		policyID = id

		// Insert rounding tiers if this is a tiered policy
		if policy.RoundingType == "tiered" && len(policy.RoundingTiers) > 0 {
			tierDataSlice := make([]map[string]interface{}, len(policy.RoundingTiers))
			for i, tier := range policy.RoundingTiers {
				tierDataSlice[i] = map[string]interface{}{
					"overtime_policy_id":    policyID,
					"up_to_minutes":         tier.UpToMinutes,
					"rounded_value_minutes": tier.RoundedValueMinutes,
				}
			}

			// Bulk insert tiers
			tx.BulkInsert("hrm_overtime_rounding_tiers", tierDataSlice)
		}

		return nil
	})

	if err != nil {
		log.IfError(err)
		return 0, err
	}

	return policyID, nil
}

// FetchOvertimePolicies retrieves overtime policies with optional filters
func (m *mySQLOvertimeRepository) FetchOvertimePolicies(adminFkid int, filter domain.OvertimePolicyFilter) ([]domain.OvertimePolicyDetail, error) {
	// Build the main query for policies with employee count
	query := `SELECT
		op.id, op.policy_name, op.description, op.rounding_type,
		op.rounding_minutes, op.rounding_method, op.admin_fkid,
		op.created_at, op.updated_at,
		COALESCE(COUNT(he.hrm_employee_id), 0) as employee_count
	FROM hrm_overtime_policies op
	LEFT JOIN hrm_employee he ON he.overtime_policies_fkid = op.id AND he.data_status = 'on'
	WHERE op.admin_fkid = @adminFkid`

	// Build parameters map
	params := map[string]any{
		"adminFkid": adminFkid,
	}

	// Add optional filters
	if filter.RoundingType != "" {
		query += ` AND op.rounding_type = @roundingType`
		params["roundingType"] = filter.RoundingType
	}

	if filter.PolicyName != "" {
		query += ` AND op.policy_name LIKE @policyName`
		params["policyName"] = "%" + filter.PolicyName + "%"
	}

	query += ` GROUP BY op.id, op.policy_name, op.description, op.rounding_type, op.rounding_minutes, op.rounding_method, op.admin_fkid, op.created_at, op.updated_at`
	query += ` ORDER BY op.created_at DESC`

	// Use MapParam to handle named parameters
	query, paramValues := mysql.MapParam(query, params)

	// Define a struct to capture the query result with employee count
	type policyWithCount struct {
		domain.OvertimePolicy
		EmployeeCount int `db:"employee_count"`
	}

	var policiesWithCount []policyWithCount
	err := m.Query(query, paramValues...).Model(&policiesWithCount)
	if log.IfError(err) {
		return nil, err
	}

	// Convert to detail format and fetch tiers for tiered policies
	var policyDetails []domain.OvertimePolicyDetail
	for _, policyWithCount := range policiesWithCount {
		detail := domain.OvertimePolicyDetail{
			OvertimePolicy: policyWithCount.OvertimePolicy,
			EmployeeCount:  policyWithCount.EmployeeCount,
		}

		// Fetch tiers if this is a tiered policy
		if policyWithCount.RoundingType == "tiered" {
			tiers, err := m.fetchRoundingTiers(policyWithCount.ID)
			if err != nil {
				log.IfError(err)
				return nil, err
			}
			detail.RoundingTiers = tiers
		}

		policyDetails = append(policyDetails, detail)
	}

	return policyDetails, nil
}

// fetchRoundingTiers retrieves rounding tiers for a specific policy
func (m *mySQLOvertimeRepository) fetchRoundingTiers(policyID int64) ([]domain.OvertimeRoundingTier, error) {
	query := `SELECT 
		id, overtime_policy_id, up_to_minutes, rounded_value_minutes
	FROM hrm_overtime_rounding_tiers 
	WHERE overtime_policy_id = ? 
	ORDER BY up_to_minutes ASC`

	var tiers []domain.OvertimeRoundingTier
	err := m.Query(query, policyID).Model(&tiers)
	if log.IfError(err) {
		return nil, err
	}

	return tiers, nil
}

// AssignOvertimePolicyToEmployees assigns an overtime policy to multiple employees
func (m *mySQLOvertimeRepository) AssignOvertimePolicyToEmployees(request domain.OvertimePolicyAssignmentRequest, adminFkid int) (domain.OvertimePolicyAssignmentResponse, error) {
	response := domain.OvertimePolicyAssignmentResponse{}

	// First, verify that the overtime policy exists and belongs to the admin
	policyQuery := `SELECT id FROM hrm_overtime_policies WHERE id = ? AND admin_fkid = ?`
	policyResult, err := m.Query(policyQuery, request.OvertimePolicyID, adminFkid).Map()
	if err != nil || len(policyResult) == 0 {
		return response, fmt.Errorf("overtime policy not found or access denied")
	}

	// Verify that all employees exist and belong to the admin
	if len(request.EmployeeIDs) == 0 {
		return response, fmt.Errorf("no employee IDs provided")
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(request.EmployeeIDs))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	// Verify employees exist and belong to admin
	employeeQuery := fmt.Sprintf(`SELECT hrm_employee_id FROM hrm_employee
		WHERE hrm_employee_id IN (%s) AND admin_fkid = ? AND data_status = 'on'`, placeholders)

	// Convert employee IDs to []any and add adminFkid
	args := make([]any, len(request.EmployeeIDs)+1)
	for i, id := range request.EmployeeIDs {
		args[i] = id
	}
	args[len(request.EmployeeIDs)] = adminFkid

	validEmployees, err := m.QueryArrayOld(employeeQuery, args...)
	if err != nil {
		return response, fmt.Errorf("error validating employees: %v", err)
	}

	// Create map of valid employee IDs for quick lookup
	validEmployeeMap := make(map[int]bool)
	for _, emp := range validEmployees {
		if empID, ok := emp["hrm_employee_id"].(int64); ok {
			validEmployeeMap[int(empID)] = true
		}
	}

	// Track failed assignments
	var failedIDs []int
	var errors []string
	successCount := 0

	// Update employees with the overtime policy
	err = m.WithTransaction(func(tx mysql.Transaction) error {
		for _, employeeID := range request.EmployeeIDs {
			if !validEmployeeMap[employeeID] {
				failedIDs = append(failedIDs, employeeID)
				errors = append(errors, fmt.Sprintf("Employee ID %d not found or inactive", employeeID))
				continue
			}

			// Update the employee's overtime policy
			updateData := map[string]any{
				"overtime_policies_fkid": request.OvertimePolicyID,
				"data_modified":          time.Now().UnixMilli(),
			}

			tx.Update("hrm_employee", updateData, "hrm_employee_id = ?", employeeID)
			successCount++
		}
		return nil
	})

	if err != nil {
		return response, fmt.Errorf("error updating employees: %v", err)
	}

	response.SuccessCount = successCount
	response.FailedCount = len(failedIDs)
	response.FailedIDs = failedIDs
	response.Errors = errors

	if successCount > 0 && len(failedIDs) == 0 {
		response.Message = fmt.Sprintf("Successfully assigned overtime policy to %d employees", successCount)
	} else if successCount > 0 && len(failedIDs) > 0 {
		response.Message = fmt.Sprintf("Partially successful: assigned to %d employees, failed for %d employees", successCount, len(failedIDs))
	} else {
		response.Message = "Failed to assign overtime policy to any employees"
	}

	return response, nil
}

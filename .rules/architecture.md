# Project Architecture Rules for AI Code Agents

## Overview
This project follows a clean architecture pattern with clear separation of concerns across three main layers: Handler, UseCase, and Repository. All communication between layers uses interfaces to ensure loose coupling and testability.

## Directory Structure
```
module_name/
├── delivery/
│   └── http/
│       └── module_handler.go
├── usecase/
│   └── module_usecase.go
└── repository/
    └── mysql/
        └── module_repository.go
```

## Layer Responsibilities

### 1. Handler Layer (`delivery/http/`)
**Purpose**: Handle incoming HTTP requests from clients and return appropriate responses.

**Responsibilities**:
- Parse HTTP request parameters and body
- Validate request format and basic structure
- Call appropriate UseCase methods
- Format and return HTTP responses
- Handle HTTP status codes and error responses
- Manage authentication and authorization

**Key Patterns**:
- Use Fiber framework for HTTP handling
- Always use `@Security BearerAuth` for all endpoints requiring authentication
- Extract user session using `domain.GetUserSessionFiber(c)`
- Validate user session and BusinessId before processing requests
- Use consistent error response format:
  ```go
  return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
      "message": "error message",
      "status":  0,
      "error":   err.<PERSON><PERSON>r(),
  })
  ```
- Use consistent success response format:
  ```go
  return c.<PERSON>(data) // for GET requests
  return c.Status(fiber.StatusCreated).JSON(&fiber.Map{
      "message": "success message",
      "status":  1,
      "data":    result,
  }) // for POST/PUT requests
  ```

**Swagger Documentation Requirements**:
- All endpoints must have complete Swagger documentation
- Include `@Summary`, `@Description`, `@Tags`, `@Accept`, `@Produce`
- Always include `@Security BearerAuth` for authenticated endpoints
- Document all parameters with `@Param`
- Document all response codes with `@Success` and `@Failure`
- Include example response objects
- Use `@Router` to specify the endpoint path and method

### 2. UseCase Layer (`usecase/`)
**Purpose**: Implement all business logic, validation, and orchestration.

**Responsibilities**:
- Implement complex business rules and logic
- Perform data validation using `github.com/gookit/validate`
- Orchestrate calls to multiple repositories if needed
- Handle business-specific error cases
- Transform data between domain models
- Implement custom validation rules when needed

**Key Patterns**:
- Use `github.com/gookit/validate` for all validation
- Register custom validators in the constructor:
  ```go
  func NewModuleUseCase(repo domain.ModuleRepository) domain.ModuleUseCase {
      validate.AddValidator("custom_rule", customValidatorFunc)
      return &moduleUseCase{moduleRepository: repo}
  }
  ```
- Handle validation errors consistently:
  ```go
  v := data.Create()
  if !v.Validate() {
      return 0, domain.ValidationException{
          Message: v.Errors.Error(), 
          ValidatinFieldsErr: v.Errors.All()
      }
  }
  ```
- Use domain exceptions for business logic errors
- Keep business logic separate from data access logic

### 3. Repository Layer (`repository/mysql/`)
**Purpose**: Handle all database communication and data persistence.

**Responsibilities**:
- Execute database queries and commands
- Map database results to domain models
- Handle database transactions
- Implement data access patterns
- Manage database connections and resources

**Key Patterns**:

#### Query Parameters
Always use the custom `mysql.MapParam` function for parameterized queries:
```go
query := `SELECT * FROM table WHERE column = @paramName AND other_column = @otherParam`
query, params := mysql.MapParam(query, map[string]any{
    "paramName": value,
    "otherParam": otherValue,
})
```

#### Data Parsing
Use the custom `Model()` method to parse query results to structs:
```go
var results []domain.ModelType
err := m.Query(query, params...).Model(&results)
if log.IfError(err) {
    return nil, err
}
```

#### Transactions
Use the built-in transaction wrapper for operations requiring atomicity:
```go
err := m.WithTransaction(func(tx mysql.Transaction) error {
    // Perform multiple database operations
    result1 := tx.Insert("table1", data1)
    result2 := tx.Update("table2", data2, "id = ?", id)
    return nil
})
```

#### Available Transaction Methods
- `tx.Insert(table, data)` - Insert single record
- `tx.Update(table, data, whereCondition, whereParams...)` - Update records
- `tx.Delete(table, whereMap)` - Delete records
- `tx.BulkInsert(table, dataSlice)` - Insert multiple records
- `tx.BatchUpdate(table, dataSlice, whereColumn)` - Update multiple records

## Interface Communication

### Domain Interfaces
All layers communicate through interfaces defined in the `domain/` package:
- `ModuleUseCase` interface - defines business operations
- `ModuleRepository` interface - defines data access operations
- Common contract interface for shared operations

### Dependency Injection
- Handlers depend on UseCase interfaces
- UseCases depend on Repository interfaces
- Repositories implement the interfaces
- Use constructor functions to wire dependencies

## Utilities (`core/` directory)

### Available Utilities
- `core/log` - Logging utilities with error handling
- `core/mysql` - Database operations and utilities
- `core/util/array` - Array manipulation utilities
- `core/util/cast` - Type casting utilities
- `core/util/request` - HTTP request utilities
- `core/util/token` - Token handling utilities

### Common Utility Functions
- `log.IfError(err)` - Log errors if they exist
- `mysql.MapParam(query, params)` - Parameterize SQL queries
- `cast.ToString(value)` - Safe type conversion

## Error Handling

### Domain Exceptions
Use custom domain exceptions for business logic errors:
```go
type ValidationException struct {
    Message            string
    ValidatinFieldsErr map[string][]string
}
```

### Error Response Patterns
- Repository layer: Return errors, let upper layers handle them
- UseCase layer: Transform errors into domain exceptions when appropriate
- Handler layer: Convert errors to appropriate HTTP status codes and responses

## Validation Rules

### Request Validation
Use struct tags with `github.com/gookit/validate`:
```go
type RequestStruct struct {
    Field1 string `json:"field1" validate:"required" message:"required:Field1 is required"`
    Field2 int    `json:"field2" validate:"required|gt:0" message:"required:Field2 is required|gt:Field2 must be greater than 0"`
}
```

### Custom Validators
Register custom validators in UseCase constructors:
```go
validate.AddValidator("custom_rule", func(val interface{}) bool {
    // Custom validation logic
    return true
})
```

## Security Requirements

### Authentication
- All endpoints require Bearer token authentication
- Use `@Security BearerAuth` in Swagger documentation
- Extract user session: `user := domain.GetUserSessionFiber(c)`
- Validate BusinessId before processing: `if user.BusinessId == 0 { return unauthorized }`

### Data Access Control
- Filter all data by user's BusinessId (admin_fkid)
- Never expose data from other businesses
- Always include BusinessId in database queries

## Testing Guidelines

### Unit Testing
- Test each layer independently using mocks
- Focus on business logic testing in UseCase layer
- Test error scenarios and edge cases
- Use table-driven tests for multiple scenarios

### Integration Testing
- Test handler endpoints with real HTTP requests
- Test database operations with test database
- Verify complete request-response cycles

## Code Quality Standards

### Naming Conventions
- Use descriptive names for functions and variables
- Follow Go naming conventions (PascalCase for exported, camelCase for unexported)
- Use consistent naming across layers (e.g., `FetchUsers`, `AddUser`, `UpdateUser`)

### Documentation
- Document all exported functions and types
- Include usage examples for complex functions
- Keep Swagger documentation up to date
- Document business rules and constraints

### Error Messages
- Provide clear, user-friendly error messages
- Include field-specific validation errors
- Use consistent error response formats
- Log detailed errors for debugging while returning safe messages to clients

# Overtime Policy Assignment API Examples

## API Endpoint
```
POST /v1/overtime-policies/assign
Authorization: Bearer <token>
Content-Type: application/json
```

## Request Examples

### 1. Assign Single Employee
```json
{
  "overtime_policy_id": 1,
  "employee_ids": [123]
}
```

### 2. Assign Multiple Employees
```json
{
  "overtime_policy_id": 2,
  "employee_ids": [123, 456, 789, 101]
}
```

### 3. Bulk Assignment (up to 100 employees)
```json
{
  "overtime_policy_id": 3,
  "employee_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
}
```

## Response Examples

### Success Response (All employees assigned)
```json
{
  "message": "Successfully assigned overtime policy to 4 employees",
  "status": 1,
  "success_count": 4,
  "failed_count": 0,
  "failed_ids": null,
  "errors": null
}
```

### Partial Success Response (Some employees failed)
```json
{
  "message": "Partially successful: assigned to 3 employees, failed for 1 employees",
  "status": 1,
  "success_count": 3,
  "failed_count": 1,
  "failed_ids": [999],
  "errors": ["Employee ID 999 not found or inactive"]
}
```

### Validation Error Response
```json
{
  "message": "Validation failed",
  "status": 0,
  "errors": {
    "overtime_policy_id": {
      "required": "overtime_policy_id is required"
    },
    "employee_ids": {
      "required": "employee_ids is required",
      "minLen": "employee_ids must contain at least 1 item"
    }
  }
}
```

### Business Logic Error Response
```json
{
  "message": "Cannot assign overtime policy to more than 100 employees at once",
  "status": 0,
  "errors": {
    "employee_ids": {
      "limit": "Maximum 100 employees allowed per request"
    }
  }
}
```

## Key Features

1. **Validation**: Validates overtime policy exists and belongs to the authenticated user
2. **Employee Verification**: Ensures all employees exist, are active, and belong to the user's business
3. **Batch Processing**: Supports up to 100 employees per request
4. **Duplicate Removal**: Automatically removes duplicate employee IDs
5. **Partial Success**: Reports both successful and failed assignments
6. **Transaction Safety**: Uses database transactions for data consistency
7. **Overwrite Policy**: Each employee can only have one overtime policy (overwrites existing)

## Database Impact

The API updates the `hrm_employee` table:
- Sets `overtime_policies_fkid` to the specified policy ID
- Updates `data_modified` timestamp
- Only affects employees that belong to the authenticated user's business
- Only affects active employees (`data_status = 'on'`)

package mysql

import (
	"database/sql"
	"encoding/json"
	"time"

	log "gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLScheduleRepository struct {
	mysql.Repository
}

// NewMySQLScheduleRepository func
func NewMySQLScheduleRepository(conn *sql.DB) domain.ScheduleRepository {
	return &mySQLScheduleRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLScheduleRepository) Fetch(outletID int, employeeID []int, month string, year string, startDate string) ([]domain.DataSchedule, error) {
	whereIN := ""
	args := []interface{}{outletID, startDate, startDate, startDate, startDate, outletID}
	if len(employeeID) != 0 && employeeID[0] != 0 {
		whereIN += "AND e.employee_id IN("
		for _, v := range employeeID {
			whereIN += " ?,"
			args = append(args, v)
		}
		whereIN = whereIN[:len(whereIN)-1] + ")"
	}
	query := "SELECT ts.schedule_month, ts.schedule_date, ts.shift_id, e.employee_id AS employee_id, e.name AS employee_name, ts.outlet_id, o.name AS outlet_name, he.type_fkid, he.nik, het.type_name, htc.dtrc_date AS htc_date, htc.type AS htc_type, htc.dtrc_reason AS trc_reason, htc.dtrc_status FROM employee AS e LEFT JOIN (SELECT ts.* FROM hrm_trans_schedule AS ts WHERE ts.outlet_id = ? AND schedule_date > DATE_FORMAT(?, '%Y-%m-%d') - INTERVAL 1 DAY AND schedule_date < DATE_FORMAT(?, '%Y-%m-%d') + INTERVAL 1 MONTH) AS ts ON ts.employee_id = e.employee_id LEFT JOIN hrm_detail_trans_cuti AS htc ON htc.employee_fkid = e.employee_id AND dtrc_date >= DATE_FORMAT(?, '%Y-%m-%d') - INTERVAL 1 DAY AND dtrc_date <= DATE_FORMAT(?, '%Y-%m-%d') + INTERVAL 1 MONTH JOIN employee_outlet eo ON eo.employee_fkid = e.employee_id JOIN outlets o ON o.outlet_id = eo.outlet_fkid JOIN hrm_employee he ON he.employee_fkid = e.employee_id JOIN hrm_employee_type het ON het.type_id = he.type_fkid WHERE eo.outlet_fkid = ? " + whereIN + " ORDER BY e.name ASC"

	res, err := m.QueryArrayOld(query, args...)
	log.IfError(err)
	resultJSON, err := json.Marshal(res)
	var results []domain.DataSchedule
	err = json.Unmarshal(resultJSON, &results)
	return results, nil
}

func (m *mySQLScheduleRepository) FetchEmployeeSchedule(outletID int, employeeID int) ([]domain.EmployeeSchedule, error) {
	args := []interface{}{outletID}
	where := ""
	if employeeID != 0 {
		args = append(args, employeeID)
		where = "AND he.hrm_employee_id = ?"
	}

	query := "SELECT hts.schedule_id, hts.outlet_id, hts.hrm_employee_fkid as hrm_employee_id, hts.schedule_date, hts.shift_id, hms.shift_in, hms.shift_out, he.name AS employee_name, he.nik AS nik, hms.shift_color FROM hrm_trans_schedule hts JOIN hrm_employee he ON he.hrm_employee_id=hts.hrm_employee_fkid JOIN hrm_master_shift hms ON hms.shift_code = hts.shift_id WHERE hts.outlet_id = ? " + where + " AND hts.schedule_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND hts.schedule_date < DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, '%Y-%m-01') ORDER BY hts.schedule_date ASC"

	res, err := m.QueryArrayOld(query, args...)
	log.IfError(err)
	resultJSON, err := json.Marshal(res)

	if err != nil {
		return []domain.EmployeeSchedule{}, err
	}

	var results []domain.EmployeeSchedule

	err = json.Unmarshal(resultJSON, &results)
	return results, err
}

func (m *mySQLScheduleRepository) FetchShiftEmployee(outletID int) ([]domain.ShiftEmployee, error) {
	query := "SELECT shift_id, shift_type, shift_code, IFNULL(shift_color, '#FFFFFF') AS shift_color FROM hrm_master_shift WHERE shift_active = 1 AND shift_office = ?"
	shifts, err := m.QueryArrayOld(query, outletID)
	log.IfError(err)
	resultJSON, err := json.Marshal(shifts)
	log.IfError(err)
	var results []domain.ShiftEmployee
	err = json.Unmarshal(resultJSON, &results)
	return results, err
}

func (m *mySQLScheduleRepository) GetColumns(date string) ([]domain.Header, error) {
	args := []interface{}{date, date, date, date, date}

	query := "SELECT a.tanggal, DATE_FORMAT(a.tanggal, '%d') AS DATE, DATE_FORMAT(a.tanggal, '%d/%m/%Y') AS date_header, CASE WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'sun' THEN 'Mi' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'mon' THEN 'Se' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'tue' THEN 'Sel' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'wed' THEN 'Ra' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'thu' THEN 'Ka' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'fri' THEN 'Ju' WHEN LOWER( SUBSTRING( DAYNAME( DATE_FORMAT(a.tanggal, '%Y-%m-%d') ), 1, 3 ) ) = 'sat' THEN 'Sa' ELSE '' END hari FROM ( SELECT ( SELECT ? + INTERVAL 1 MONTH - INTERVAL 1 DAY ) - INTERVAL( a.a +(10 * b.a) +(100 * c.a) +(1000 * d.a) ) DAY AS tanggal FROM ( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 ) AS a CROSS JOIN( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 ) AS b CROSS JOIN( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 ) AS c CROSS JOIN( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 ) AS d ) a WHERE a.tanggal BETWEEN ? AND( CASE WHEN DATE_FORMAT(?, '%d') = '01' THEN( SELECT LAST_DAY(?) ) ELSE ( SELECT ? + INTERVAL 1 MONTH - INTERVAL 1 DAY ) END ) ORDER BY a.tanggal ASC"

	col, err := m.QueryArrayOld(query, args...)
	log.IfError(err)
	resultJSON, err := json.Marshal(col)
	log.IfError(err)
	var results []domain.Header
	err = json.Unmarshal(resultJSON, &results)
	return results, err
}

func (m *mySQLScheduleRepository) CreateHeader(date string) (map[string]interface{}, error) {
	columns, err := m.GetColumns(date)
	log.IfError(err)
	header := make([]string, 0)
	header = []string{"Nama", "Outlet", "Tipe", "Bulan"}
	var headers []interface{}
	nested := make([]string, 0)
	nested = []string{"", "", "", ""}
	columnData := make([]map[string]interface{}, 0)
	columnData = []map[string]interface{}{
		{"data": "employee_name", "type": "text", "readOnly": true},
		{"data": "outlet", "type": "text", "readOnly": true},
		{"data": "type", "type": "text", "readOnly": true},
		{"data": "bulan", "type": "text", "readOnly": true},
	}

	shifts := make([]interface{}, 0)

	for _, v := range columns {
		dt, _ := time.Parse("2006-01-02", v.Tanggal)
		day := dt.Day()
		columnData = append(columnData, map[string]interface{}{"data": day, "type": "autocomplete", "source": shifts})
		header = append(header, v.Hari)
		nested = append(nested, v.DateHeader)
	}
	headers = []interface{}{nested, header}
	response := make(map[string]interface{})
	response["column"] = columnData
	response["header"] = header
	response["nested"] = headers
	return response, nil
}

func (m *mySQLScheduleRepository) GetColFunc(date string) ([]interface{}, error) {
	return []interface{}{}, nil
}

func (m *mySQLScheduleRepository) SaveDataSchedule(data []map[string]interface{}) error {
	dataInserts := make([]map[string]interface{}, 0)
	value := make(map[string]interface{}, 0)
	insert := make(map[string]interface{}, 0)
	for _, v := range data {
		if len(v["month"].([]string)) > 0 {
			for i := 0; i < len(v["month"].([]string)); i++ {
				value["employee_id"] = v["employee_id"]
				value["outlet_id"] = v["outlet_id"]
				value["schedule_month"] = v["month"].([]string)[i]
				_, err := m.Deletes("hrm_trans_schedule", value)
				log.IfError(err)
			}
		} else {
			value["employee_id"] = v["employee_id"]
			value["outlet_id"] = v["outlet_id"]
			value["schedule_month"] = v["month"].([]string)[0]
			_, err := m.Deletes("hrm_trans_schedule", value)
			log.IfError(err)
		}

		if len(v["month"].([]string)) == len(v["date"].([]map[string]interface{})) {
			for _, w := range v["date"].([]map[string]interface{}) {
				insert["outlet_id"] = v["outlet_id"]
				insert["employee_id"] = v["employee_id"]
				for m := range w {
					d, _ := time.Parse("2006-01-02", m)
					dat := d.Format("2006-01")
					insert["shift_id"] = w[m]
					insert["schedule_date"] = m
					insert["schedule_month"] = dat
				}
				dataInserts = append(dataInserts, insert)
				insert = map[string]interface{}{}
			}
		}
	}
	_, err := m.BulkInsert("hrm_trans_schedule", dataInserts)
	if err != nil {
		return err
	}
	return nil
}

package logger

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"reflect"
	"runtime"
	"strings"
	"time"
)

const (
	reset  = "\033[0m"
	red    = "\033[31m"
	green  = "\033[32m"
	yellow = "\033[33m"
	cyan   = "\033[36m"
	purple = "\033[35m"
)

type logLevel struct {
	color string
	label string
}

var (
	infoLevel  = logLevel{color: green, label: "INFO"}
	debugLevel = logLevel{color: cyan, label: "DEBUG"}
	warnLevel  = logLevel{color: yellow, label: "WARN"}
	errorLevel = logLevel{color: red, label: "ERROR"}
	fatalLevel = logLevel{color: purple, label: "FATAL"}
)

func isStructOrMap(v any) bool {
	if v == nil {
		return false
	}
	kind := reflect.TypeOf(v).Kind()
	return kind == reflect.Struct || kind == reflect.Map || kind == reflect.Slice
}

func PrettyPrintObject(v any) string {
	b, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		return fmt.Sprintf("Failed to marshal JSON: %v", err)
	}
	return string(b)
}

func logMessage(level logLevel, format string, args ...interface{}) {
	pc, file, line, _ := runtime.Caller(2)
	funcName := runtime.FuncForPC(pc).Name()
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	if len(args) == 1 && isStructOrMap(args[0]) {
		args[0] = PrettyPrintObject(args[0])
	}

	message := fmt.Sprintf(format, args...)

	output := fmt.Sprintf(
		"[%s] %s | %s:%d | %s\n=> %s",
		level.label, timestamp, file, line, funcName, message,
	)

	printOutput := output
	if !isProduction {
		printOutput = fmt.Sprintf("%s%s%s", level.color, output, reset)
	}

	log.Println(printOutput)

	if logFilePath != "" {
		writeLogToFile(stripColor(output))
	}
}

func stripColor(s string) string {
	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(
		s, reset, ""), red, ""), green, ""), yellow, ""), cyan, "")
}

func writeLogToFile(content string) {
	f, err := os.OpenFile(logFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return
	}
	defer f.Close()

	logger := log.New(f, "", 0)
	logger.Println(content)
}

func Info(format string, args ...any) {
	logMessage(infoLevel, format, args...)
}

func Debug(format string, args ...any) {
	logMessage(debugLevel, format, args...)
}

func Warn(format string, args ...any) {
	logMessage(warnLevel, format, args...)
}

func Error(format string, args ...any) {
	logMessage(errorLevel, format, args...)
}

func Fatal(format string, args ...any) {
	logMessage(fatalLevel, format, args...)
	log.Fatalf(format, args...)
}

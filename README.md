### API HRM

this app serve as backend to handle any request related to HR App

## REQUISITES

- create jwt private and public key

```aidl
openssl genrsa -out assets/rsa/priv.key 2048
openssl rsa -in assets/rsa/priv.key -pubout > assets/rsa/pub.key
```

## Swagger

First make you you have installed [swaggo](https://github.com/swaggo/swag) :

```
go install github.com/swaggo/swag/cmd/swag@latest
```

To generate swagger documentation:

```
$HOME/go/bin/swag init --parseDependency
```

## AUTO RELOADING

**using air (auto reloading)**

```bash
go install github.com/air-verse/air@latest
```

create file .air.toml in root directory

base configuration

```bash
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
cmd = "go build -o ./tmp/main.exe main.go"
bin = "tmp/main.exe"
full_bin = "./tmp/main.exe"
delay = 1000                                   # Delay in milliseconds before restarting

[watcher]
# Watch all folders
paths = ["."]
# File extensions to monitor
exts = ["go", "html", "tpl", "env"]
# Directories to ignore
ignore_dirs = ["tmp", "vendor", "node_modules"]

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
main_only = false
silent = false
time = false

[misc]
clean_on_exit = true

[proxy]
app_port = 0
enabled = false
proxy_port = 0

[screen]
clear_on_rebuild = false
keep_scroll = true
```

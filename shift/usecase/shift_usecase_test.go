package usecase

import (
	"testing"

	"gitlab.com/backend/api-hrm/domain"
)

// MockShiftRepository is a mock implementation of domain.ShiftRepository for testing
type MockShiftRepository struct {
	shifts      map[string]int // map of shift name to ID
	types       map[string]int // map of type name to ID
	nextShiftID int
}

func NewMockShiftRepository() *MockShiftRepository {
	return &MockShiftRepository{
		shifts:      make(map[string]int),
		types:       make(map[string]int),
		nextShiftID: 1,
	}
}

// Implement required methods for the interface
func (m *MockShiftRepository) Fetch(adminFkid int) ([]domain.MasterShift, error) {
	return nil, nil
}

func (m *MockShiftRepository) FetchWithFilter(adminFkid int, filter domain.ShiftFilter) ([]domain.MasterShift, error) {
	return nil, nil
}

func (m *MockShiftRepository) FetchShiftGroup(outletFkid []interface{}) ([]domain.ShiftGroup, error) {
	return nil, nil
}

func (m *MockShiftRepository) Add(shifts []map[string]interface{}) error {
	return nil
}

func (m *MockShiftRepository) Update(shift []map[string]interface{}) error {
	return nil
}

func (m *MockShiftRepository) Delete(shift domain.ShiftAdd) error {
	return nil
}

func (m *MockShiftRepository) UpdateHot(shifts []map[string]interface{}) error {
	return nil
}

func (m *MockShiftRepository) FetchShiftByName(adminID int, shiftNames []string) ([]domain.ShiftValidation, error) {
	var validShifts []domain.ShiftValidation
	for _, name := range shiftNames {
		if id, exists := m.shifts[name]; exists {
			validShifts = append(validShifts, domain.ShiftValidation{
				Name: name,
				ID:   id,
			})
		}
	}
	return validShifts, nil
}

func (m *MockShiftRepository) FetchTypeByName(adminID int, typeNames []string) ([]domain.TypeValidation, error) {
	var validTypes []domain.TypeValidation
	for _, name := range typeNames {
		if id, exists := m.types[name]; exists {
			validTypes = append(validTypes, domain.TypeValidation{
				TypeName: name,
				TypeID:   id,
			})
		}
	}
	return validTypes, nil
}

func (m *MockShiftRepository) GetShiftNamesForDropdown(adminID int) ([]string, error) {
	return nil, nil
}

func (m *MockShiftRepository) GetTypeNamesForDropdown(adminID int) ([]string, error) {
	return nil, nil
}

func (m *MockShiftRepository) CreateShifts(shiftNames []string, adminID int) ([]int, error) {
	var shiftIDs []int
	for _, name := range shiftNames {
		id := m.nextShiftID
		m.shifts[name] = id
		shiftIDs = append(shiftIDs, id)
		m.nextShiftID++
	}
	return shiftIDs, nil
}

func (m *MockShiftRepository) ImportShifts(outletIDs []int, shifts []domain.ShiftImportWithId, adminID int) error {
	return nil
}

func TestImportShifts_CreatesMissingShifts(t *testing.T) {
	// Setup
	mockRepo := NewMockShiftRepository()

	// Add existing shift and type
	mockRepo.shifts["Morning Shift"] = 1
	mockRepo.types["Full Time"] = 1

	useCase := NewShiftUseCase(mockRepo)

	// Test data with one existing shift and one new shift
	shifts := []domain.ShiftImport{
		{
			ShiftName:      "Morning Shift", // existing
			TypeName:       "Full Time",
			ShiftCode:      "MS",
			TimeIn:         "08:00",
			TimeOut:        "17:00",
			ShiftTolerance: "15",
			ShiftColor:     "#FFFFFF",
			ShiftActive:    "yes",
		},
		{
			ShiftName:      "Evening Shift", // new - should be created
			TypeName:       "Full Time",
			ShiftCode:      "ES",
			TimeIn:         "14:00",
			TimeOut:        "23:00",
			ShiftTolerance: "15",
			ShiftColor:     "#FFFF00",
			ShiftActive:    "yes",
		},
	}

	outletIDs := []int{1, 2}
	adminID := 1

	// Execute
	err := useCase.ImportShifts(outletIDs, shifts, adminID)

	// Verify
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	// Check that the new shift was created
	if _, exists := mockRepo.shifts["Evening Shift"]; !exists {
		t.Error("Expected 'Evening Shift' to be created")
	}

	// Check that both shifts now exist
	if len(mockRepo.shifts) != 2 {
		t.Errorf("Expected 2 shifts, got %d", len(mockRepo.shifts))
	}
}

func TestImportShifts_ErrorOnMissingType(t *testing.T) {
	// Setup
	mockRepo := NewMockShiftRepository()
	useCase := NewShiftUseCase(mockRepo)

	// Test data with missing type
	shifts := []domain.ShiftImport{
		{
			ShiftName:      "Morning Shift",
			TypeName:       "Non-existent Type", // This type doesn't exist
			ShiftCode:      "MS",
			TimeIn:         "08:00",
			TimeOut:        "17:00",
			ShiftTolerance: "15",
		},
	}

	outletIDs := []int{1}
	adminID := 1

	// Execute
	err := useCase.ImportShifts(outletIDs, shifts, adminID)

	// Verify
	if err == nil {
		t.Error("Expected error for missing type, got nil")
	}

	if err != nil && !contains(err.Error(), "shift type 'Non-existent Type' not found") {
		t.Errorf("Expected error about missing type, got: %v", err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) &&
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

package domain

// OvertimePolicy represents the main overtime policy record
type OvertimePolicy struct {
	ID              int64   `json:"id" db:"id"`
	PolicyName      string  `json:"policy_name" db:"policy_name"`
	Description     string  `json:"description" db:"description"`
	RoundingType    string  `json:"rounding_type" db:"rounding_type"`
	RoundingMinutes *int    `json:"rounding_minutes" db:"rounding_minutes"`
	RoundingMethod  *string `json:"rounding_method" db:"rounding_method"`
	AdminFkid       int     `json:"admin_fkid" db:"admin_fkid"`
	CreatedAt       int64   `json:"created_at" db:"created_at"`
	UpdatedAt       int64   `json:"updated_at" db:"updated_at"`
}

// OvertimeRoundingTier represents a rounding tier for tiered policies
type OvertimeRoundingTier struct {
	ID                  int64 `json:"id" db:"id"`
	OvertimePolicyID    int64 `json:"overtime_policy_id" db:"overtime_policy_id"`
	UpToMinutes         int   `json:"up_to_minutes" db:"up_to_minutes"`
	RoundedValueMinutes int   `json:"rounded_value_minutes" db:"rounded_value_minutes"`
}

// OvertimePolicyRequest represents the request payload for creating an overtime policy
type OvertimePolicyRequest struct {
	PolicyName      string                        `json:"policy_name" validate:"required|minLen:3|maxLen:255" example:"Standard Overtime" swaggerignore:"false"`
	Description     string                        `json:"description" validate:"maxLen:1000" example:"Policy for standard overtime calculation" swaggerignore:"false"`
	RoundingType    string                        `json:"rounding_type" validate:"required|in:none,simple,tiered" example:"simple" enums:"none,simple,tiered" swaggerignore:"false"`
	RoundingMinutes *int                          `json:"rounding_minutes,omitempty" validate:"int|min:1|max:1440" example:"15" swaggerignore:"false"`
	RoundingMethod  *string                       `json:"rounding_method,omitempty" validate:"in:round_down,round_nearest,round_up" example:"round_nearest" enums:"round_down,round_nearest,round_up" swaggerignore:"false"`
	RoundingTiers   []OvertimeRoundingTierRequest `json:"rounding_tiers,omitempty" swaggerignore:"false"`
}

// OvertimeRoundingTierRequest represents a rounding tier in the request
type OvertimeRoundingTierRequest struct {
	UpToMinutes         int `json:"up_to_minutes" validate:"required|int|min:1|max:1440"`
	RoundedValueMinutes int `json:"rounded_value_minutes" validate:"required|int|min:0|max:1440"`
}

// OvertimePolicyDetail represents the complete overtime policy with tiers
type OvertimePolicyDetail struct {
	OvertimePolicy
	RoundingTiers []OvertimeRoundingTier `json:"rounding_tiers,omitempty"`
	EmployeeCount int                    `json:"employee_count"`
}

// OvertimePolicyFilter represents filter options for fetching overtime policies
type OvertimePolicyFilter struct {
	RoundingType string `json:"rounding_type,omitempty"`
	PolicyName   string `json:"policy_name,omitempty"`
}

// OvertimePolicyAssignmentRequest represents the request to assign overtime policy to employees
type OvertimePolicyAssignmentRequest struct {
	OvertimePolicyID int   `json:"overtime_policy_id" validate:"required|int|min:1"`
	EmployeeIDs      []int `json:"employee_ids" validate:"required|minLen:1"`
}

// OvertimePolicyAssignmentResponse represents the response after assignment
type OvertimePolicyAssignmentResponse struct {
	SuccessCount int      `json:"success_count"`
	FailedCount  int      `json:"failed_count"`
	FailedIDs    []int    `json:"failed_ids,omitempty"`
	Message      string   `json:"message"`
	Errors       []string `json:"errors,omitempty"`
}

// OvertimeContract interface defines the basic operations for overtime policies
type OvertimeContract interface {
	AddOvertimePolicy(policy OvertimePolicyRequest, adminFkid int) (int64, error)
	FetchOvertimePolicies(adminFkid int, filter OvertimePolicyFilter) ([]OvertimePolicyDetail, error)
	AssignOvertimePolicyToEmployees(request OvertimePolicyAssignmentRequest, adminFkid int) (OvertimePolicyAssignmentResponse, error)
}

// OvertimeUseCase interface defines the business logic operations
type OvertimeUseCase interface {
	OvertimeContract
}

// OvertimeRepository interface defines the data access operations
type OvertimeRepository interface {
	OvertimeContract
	AddOvertimePolicyWithTiers(policy OvertimePolicyRequest, adminFkid int) (int64, error)
}

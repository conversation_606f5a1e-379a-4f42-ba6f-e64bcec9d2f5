package domain

import (
	"github.com/xuri/excelize/v2"
)

// MasterShift struct
type MasterShift struct {
	ShiftID        int    `json:"shift_id"`
	ShiftIDName    int    `json:"shift_id_name"`
	ShiftFkid      int    `json:"shift_fkid"`
	ShiftName      string `json:"shift_name"`
	ShiftOffice    string `json:"shift_office"`
	OutletID       int    `json:"shift_office_id"`
	ShiftType      string `json:"shift_type"`
	TypeID         int    `json:"shift_type_id"`
	ShiftCode      string `json:"shift_code"`
	Name           string `json:"shift_name_id"`
	ShiftIn        string `json:"shift_in"`
	ShiftOut       string `json:"shift_out"`
	ShiftTolerance int    `json:"shift_tolerance"`
	ShiftMaxIn     string `json:"shift_max_in"`
	ShiftColor     string `json:"shift_color"`
	ShiftActive    int    `json:"shift_active"`
}

// ShiftGroup struct
type ShiftGroup struct {
	ShiftID    int    `json:"id"`
	Name       string `json:"shift_name"`
	ShiftFkid  int    `json:"shift_fkid"`
	OutletFkid int    `json:"outlet_fkid"`
}

// ShiftAdd body params
type ShiftAdd struct {
	ShiftID        int     `json:"shift_id"`
	ShiftType      string  `json:"shift_type"`
	ShiftCode      string  `json:"shift_code"`
	ShiftFkid      int     `json:"shift_fkid"`
	ShiftIn        *string `json:"shift_in"`
	ShiftOut       *string `json:"shift_out"`
	ShiftTolerance int     `json:"shift_tolerance"`
	ShiftColor     string  `json:"shift_color"`
	ShiftOffice    string  `json:"shift_office"`
	ShiftActive    int     `json:"shift_active"`
}

// ShiftImport struct for shift import data
type ShiftImport struct {
	ShiftCode      string `json:"shift_code"`
	ShiftName      string `json:"shift_name"`
	TimeIn         string `json:"time_in"`
	TimeOut        string `json:"time_out"`
	ShiftTolerance string `json:"tolerance"`
	TypeName       string `json:"type"`
	ShiftColor     string `json:"color"`  // Optional, if blank defaults to white (#FFFFFF)
	ShiftActive    string `json:"active"` // Optional, yes/no, if blank defaults to yes
}

type ShiftImportWithId struct {
	ShiftImport
	ShiftID int `json:"shift_id"`
	TypeID  int `json:"type_id"`
}

// ShiftValidation struct for validation results
type ShiftValidation struct {
	Name string `json:"name"`
	ID   int    `json:"id"`
}

// TypeValidation struct for validation results
type TypeValidation struct {
	TypeName string `json:"type_name"`
	TypeID   int    `json:"type_id"`
}

// ShiftFilter struct for filtering shifts
type ShiftFilter struct {
	OutletIDs []int `json:"outlet_ids"`
}

func (s ShiftImport) ToShiftImportWithId(shiftID, typeID int) ShiftImportWithId {
	return ShiftImportWithId{
		ShiftImport: s,
		ShiftID:     shiftID,
		TypeID:      typeID,
	}
}

// ShiftContract interface
type ShiftContract interface {
	Fetch(adminFkid int) ([]MasterShift, error)
	FetchWithFilter(adminFkid int, filter ShiftFilter) ([]MasterShift, error)
	FetchShiftGroup(outletFkid []interface{}) ([]ShiftGroup, error)
	Add([]map[string]interface{}) error
	Update([]map[string]interface{}) error
	Delete(ShiftAdd) error
	UpdateHot([]map[string]interface{}) error
	FetchShiftByName(adminID int, shiftNames []string) ([]ShiftValidation, error)
	FetchTypeByName(adminID int, typeNames []string) ([]TypeValidation, error)
	GetShiftNamesForDropdown(adminID int) ([]string, error)
	GetTypeNamesForDropdown(adminID int) ([]string, error)
	CreateShifts(shiftNames []string, adminID int) ([]int, error)
}

// ShiftUseCase interface
type ShiftUseCase interface {
	ShiftContract
	ImportShifts(outletIDs []int, shifts []ShiftImport, adminID int) error
	ExportToExcel(businessID int) (*excelize.File, error)
}

// ShiftRepository interface
type ShiftRepository interface {
	ShiftContract
	ImportShifts(outletIDs []int, shifts []ShiftImportWithId, adminID int) error
}

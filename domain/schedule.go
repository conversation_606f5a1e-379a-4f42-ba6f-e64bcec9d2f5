package domain

// Schedule struct
type Schedule struct {
	ScheduleID      string `json:"schedule_id"`
	OutletID        string `json:"outlet_id"`
	EmployeeID      string `json:"employee_id"`
	ScheduleMonth   string `json:"schedule_month"`
	ScheduleDate    string `json:"schedule_date"`
	ShiftID         string `json:"shift_id"`
	ScheduleUserID  string `json:"schedule_userid"`
	ScheduleCreated string `json:"schedule_created"`
}

// DataSchedule struct
type DataSchedule struct {
	ScheduleMonth string `json:"schedule_month"`
	ScheduleDate  string `json:"schedule_date"`
	ShiftID       string `json:"shift_id"`
	EmployeeID    int    `json:"employee_id"`
	EmployeeName  string `json:"employee_name"`
	TypeFkid      int    `json:"type_fkid"`
	Nik           string `json:"nik"`
	OutletID      int    `json:"outlet_id"`
	OutletName    string `json:"outlet_name"`
	TypeName      string `json:"type_name"`
	HtcDate       string `json:"htc_date"`
	HtcType       string `json:"htc_type"`
	TrcReason     string `json:"trc_reason"`
	DtrcStatus    int    `json:"dtrc_status"`
}

// EmployeeSchedule struct
type EmployeeSchedule struct {
	ScheduleId    int    `json:"schedule_id"`
	OutletId      int    `json:"outlet_id"`
	HrmEmployeeId int    `json:"hrm_employee_id"`
	ScheduleDate  string `json:"schedule_date"`
	ShiftId       string `json:"shift_id"`
	EmployeeName  string `json:"employee_name"`
	Nik           string `json:"nik"`
	ShiftColor    string `json:"shift_color"`
	ShiftIn       string `json:"shift_in"`
	ShiftOut      string `json:"shift_out"`
}

// ShiftEmployee struct
type ShiftEmployee struct {
	ShiftID    int    `json:"shift_id"`
	ShiftType  int    `json:"shift_type"`
	ShiftCode  string `json:"shift_code"`
	ShiftColor string `json:"shift_color"`
}

// Header struct
type Header struct {
	Tanggal    string `json:"tanggal"`
	Date       string `json:"date"`
	DateHeader string `json:"date_header"`
	Hari       string `json:"hari"`
}

// ScheduleContract interface
type ScheduleContract interface {
	Fetch(outletID int, employeeID []int, month string, year string, startDate string) ([]DataSchedule, error)
	FetchEmployeeSchedule(outletID int, employeeID int) ([]EmployeeSchedule, error)
	FetchShiftEmployee(outletID int) ([]ShiftEmployee, error)
	GetColumns(date string) ([]Header, error)
	CreateHeader(date string) (map[string]interface{}, error)
	GetColFunc(date string) ([]interface{}, error)
	SaveDataSchedule([]map[string]interface{}) error
}

// ScheduleUseCase interface
type ScheduleUseCase interface {
	ScheduleContract
}

// ScheduleRepository interface
type ScheduleRepository interface {
	ScheduleContract
}
